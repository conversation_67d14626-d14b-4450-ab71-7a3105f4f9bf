<!-- 顶部：已选字词横向滚动区 -->
<view class="selected-words-bar">
  <scroll-view scroll-x="true" class="selected-words-scroll">
    <block wx:for="{{selectedWords}}" wx:key="index">
      <view class="selected-word-item">
        {{item}}
        <image src="/images/icon_btnclose.png" class="remove-icon" data-word="{{item}}" bindtap="onRemoveWord" />
      </view>
    </block>
  </scroll-view>
  <view class="selected-words-actions">
    <button class="clear-btn" bindtap="onClear">清空</button>
    <button class="start-btn" bindtap="onStart">开始练习</button>
  </view>
</view>

<!-- 下部：课程列表及字词选择 -->
<scroll-view scroll-y="true" class="course-list-scroll">
  <block wx:for="{{courseList}}" wx:key="id">
    <view class="course-item">
      <view class="course-title" bindtap="onToggleCourse" data-id="{{item.id}}">
        {{item.name}}
        <text class="arrow">{{item.expanded ? '▼' : '▶'}}</text>
      </view>
      <view wx:if="{{item.expanded}}" class="word-list">
        <block wx:for="{{item.words}}" wx:key="word">
          <view class="word-btn {{selectedWordsSet[item] ? 'selected' : ''}}" bindtap="onSelectWord" data-word="{{item}}">
            {{item}}
          </view>
        </block>
      </view>
    </view>
  </block>
</scroll-view> 