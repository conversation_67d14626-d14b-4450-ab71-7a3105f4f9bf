<!-- 顶部：已选字词横向滚动区 -->
<view class="selected-words-bar">
  <view class="selected-words-header">
    <text class="selected-words-title">已选择字词</text>
    <text class="selected-words-count">{{selectedWords.length}}个</text>
  </view>
  <scroll-view scroll-x="true" class="selected-words-scroll">
    <view class="selected-words-container">
      <block wx:for="{{selectedWords}}" wx:key="index">
        <view class="selected-word-item">
          <!-- 字词田字格容器 -->
          <view class="word-container">
            <view class="character-grid">
              <view
                class="tianzige"
                wx:for="{{item}}"
                wx:key="charIndex"
                wx:for-item="char"
                wx:for-index="charIndex"
              >
                <view class="tianzige-inner">{{char}}</view>
              </view>
            </view>
          </view>
          <view class="remove-btn" data-word="{{item}}" bindtap="onRemoveWord">
            <text class="remove-icon">×</text>
          </view>
        </view>
      </block>
      <!-- 空状态提示 -->
      <view class="empty-selected" wx:if="{{selectedWords.length === 0}}">
        <text class="empty-text">请从下方选择字词</text>
      </view>
    </view>
  </scroll-view>
  <view class="selected-words-actions">
    <button class="action-btn clear-btn" bindtap="onClear" disabled="{{selectedWords.length === 0}}">
      <text class="btn-text">清空</text>
    </button>
    <button class="action-btn start-btn" bindtap="onStart" disabled="{{selectedWords.length === 0}}">
      <text class="btn-text">开始练习</text>
    </button>
  </view>
</view>

<!-- 下部：课程列表及字词选择 -->
<scroll-view scroll-y="true" class="course-list-scroll">
  <block wx:for="{{courseList}}" wx:key="id">
    <view class="course-item">
      <view class="course-header" bindtap="onToggleCourse" data-id="{{item.id}}">
        <view class="course-title-content">
          <text class="course-title">{{item.name}}</text>
          <text class="course-count">{{item.words.length}}个字词</text>
        </view>
        <view class="course-arrow {{item.expanded ? 'expanded' : ''}}">
          <text class="arrow-icon">▶</text>
        </view>
      </view>
      <view wx:if="{{item.expanded}}" class="word-grid">
        <block wx:for="{{item.words}}" wx:key="word">
          <view class="word-item {{selectedWordsSet[item] ? 'selected' : ''}}" bindtap="onSelectWord" data-word="{{item}}">
            <!-- 字词田字格容器 -->
            <view class="word-container">
              <view class="character-grid">
                <view
                  class="tianzige"
                  wx:for="{{item}}"
                  wx:key="charIndex"
                  wx:for-item="char"
                  wx:for-index="charIndex"
                >
                  <view class="tianzige-inner">{{char}}</view>
                </view>
              </view>
            </view>
            <!-- 选中标记 -->
            <view class="word-check" wx:if="{{selectedWordsSet[item]}}">✓</view>
          </view>
        </block>
      </view>
    </view>
  </block>
</scroll-view>