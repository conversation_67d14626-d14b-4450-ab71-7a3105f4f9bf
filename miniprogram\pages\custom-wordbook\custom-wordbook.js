const SimpleStorage = require('../../utils/simpleStorage');
const app = getApp();

Page({
  data: {
    courseList: [], // 课程及字词
    selectedWords: [], // 已选字词
    selectedWordsSet: {}, // 便于高亮判断
    loading: true,
    error: '',
  },

  async onLoad() {
    try {
      // 获取当前教材
      const currentTextbook = SimpleStorage.getCurrentTextbook();
      if (!currentTextbook || !currentTextbook.publisher || !currentTextbook.grade || !currentTextbook.term) {
        this.setData({ loading: false, error: '请先在个人中心选择教材' });
        return;
      }
      const versionId = currentTextbook.publisher.id || currentTextbook.publisher;
      const gradeId = currentTextbook.grade.id || currentTextbook.grade;
      const termId = currentTextbook.term;
      const configService = app.globalData.configService;
      if (!configService.isLoaded) {
        await configService.init();
      }
      // 获取课程列表
      const lessons = await configService.getLessons(versionId, gradeId, termId);
      // 构建课程和字词结构
      const courseList = lessons.map(lesson => {
        // 合并三类字词
        let words = [];
        if (lesson.tables) {
          ['shiZiBiao', 'xieZiBiao', 'ciYuBiao'].forEach(type => {
            if (lesson.tables[type] && Array.isArray(lesson.tables[type])) {
              words = words.concat(lesson.tables[type].map(item => item.word));
            }
          });
        }
        return {
          id: lesson.id,
          name: lesson.name,
          expanded: false,
          words: words
        };
      }).filter(c => c.words.length > 0);
      // 默认展开第一个有字词的课程
      if (courseList.length > 0) courseList[0].expanded = true;
      // 读取已选字词
      const selectedWords = wx.getStorageSync('custom_wordbook_selected') || [];
      this.setData({
        courseList,
        selectedWords,
        selectedWordsSet: this.arrToSet(selectedWords),
        loading: false,
        error: ''
      });
    } catch (e) {
      this.setData({ loading: false, error: '加载课程数据失败' });
      console.error('加载课程数据失败', e);
    }
  },

  // 课程展开/收缩
  onToggleCourse(e) {
    const id = e.currentTarget.dataset.id;
    const courseList = this.data.courseList.map(item => {
      if (item.id === id) {
        return { ...item, expanded: !item.expanded };
      }
      return item;
    });
    this.setData({ courseList });
  },

  // 选择/取消字词
  onSelectWord(e) {
    const word = e.currentTarget.dataset.word;
    let { selectedWords } = this.data;
    let set = new Set(selectedWords);
    if (set.has(word)) {
      set.delete(word);
    } else {
      set.add(word);
    }
    selectedWords = Array.from(set);
    this.setData({
      selectedWords,
      selectedWordsSet: this.arrToSet(selectedWords),
    });
    wx.setStorageSync('custom_wordbook_selected', selectedWords);
  },

  // 顶部移除单个字词
  onRemoveWord(e) {
    const word = e.currentTarget.dataset.word;
    let selectedWords = this.data.selectedWords.filter(w => w !== word);
    this.setData({
      selectedWords,
      selectedWordsSet: this.arrToSet(selectedWords),
    });
    wx.setStorageSync('custom_wordbook_selected', selectedWords);
  },

  // 清空所有已选
  onClear() {
    this.setData({
      selectedWords: [],
      selectedWordsSet: {},
    });
    wx.setStorageSync('custom_wordbook_selected', []);
  },

  // 开始练习
  async onStart() {
    if (this.data.selectedWords.length === 0) {
      wx.showToast({ title: '请先选择字词', icon: 'none' });
      return;
    }
    // 查找拼音
    const currentTextbook = SimpleStorage.getCurrentTextbook();
    const versionId = currentTextbook.publisher?.id || currentTextbook.publisher;
    const gradeId = currentTextbook.grade?.id || currentTextbook.grade;
    const termId = currentTextbook.term;
    const configService = app.globalData.configService;
    if (!configService.isLoaded) {
      await configService.init();
    }
    const lessons = await configService.getLessons(versionId, gradeId, termId);
    // 构建字词-拼音映射
    const wordPinyinMap = {};
    lessons.forEach(lesson => {
      if (lesson.tables) {
        ['shiZiBiao', 'xieZiBiao', 'ciYuBiao'].forEach(type => {
          if (lesson.tables[type] && Array.isArray(lesson.tables[type])) {
            lesson.tables[type].forEach(item => {
              if (item.word && item.pinyin) {
                wordPinyinMap[item.word] = item.pinyin;
              }
            });
          }
        });
      }
    });
    // 构造带拼音的字词对象
    const wordObjs = this.data.selectedWords.map(word => ({
      id: word,
      word,
      pinyin: wordPinyinMap[word] || '',
      table: 'custom',
      originalCourseInfo: null
    }));
    const courseInfo = {
      courseId: 'custom_wordbook',
      courseName: '自定义字词本',
      courseTitle: '自定义字词本',
      publisherId: versionId,
      gradeId: gradeId,
      term: termId,
      words: wordObjs
    };
    wx.navigateTo({
      url: '/pages/practice/practice?course=' + encodeURIComponent(JSON.stringify(courseInfo))
    });
  },

  // 工具：数组转set对象
  arrToSet(arr) {
    const set = {};
    arr.forEach(w => { set[w] = true; });
    return set;
  },
}); 