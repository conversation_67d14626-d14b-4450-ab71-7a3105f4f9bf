@protobufjs/fetch
=================
[![npm](https://img.shields.io/npm/v/@protobufjs/fetch.svg)](https://www.npmjs.com/package/@protobufjs/fetch)

Fetches the contents of a file accross node and browsers.

API
---

* **fetch(path: `string`, [options: { binary: boolean } ], [callback: `function(error: ?Error, [contents: string])`]): `Promise<string|Uint8Array>|undefined`**
  Fetches the contents of a file.

**License:** [BSD 3-Clause License](https://opensource.org/licenses/BSD-3-Clause)
