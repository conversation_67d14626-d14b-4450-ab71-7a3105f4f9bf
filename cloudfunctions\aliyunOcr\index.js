// 阿里云百炼 OCR 云函数
// 使用 DashScope 兼容模式接口
// 文档: https://help.aliyun.com/zh/dashscope/developer-reference/quick-start-for-compatible-openai-api

const cloud = require('wx-server-sdk');
const crypto = require('crypto');
const request = require('request-promise');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// 配置参数，可放在环境变量或此处硬编码（不推荐）
const config = {
  apiKey: process.env.ALIYUN_DASHSCOPE_API_KEY || 'sk-2623283821f741518564c48dce6d46ff',
  baseURL: process.env.ALIYUN_DASHSCOPE_BASEURL || 'https://dashscope.aliyuncs.com/compatible-mode/v1'
};

/**
 * 云函数入口
 * @param {{ image: string }} event image 为 base64(不带前缀)
 */
exports.main = async (event) => {
  try {
    const { image } = event;
    if (!image) {
      return { code: -1, msg: '缺少图片数据', data: null };
    }

    if (!config.apiKey) {
      return { code: -1, msg: '缺少 API Key', data: null };
    }

    const url = `${config.baseURL}/chat/completions`;

    const payload = {
      model: 'qwen-vl-ocr',
      messages: [
        {
          role: 'user',
          content: [
            { type: 'image_url', image_url: { url: `data:image/jpeg;base64,${image}` } },
            { type: 'text', text: '请识别图片中的文字' }
          ]
        }
      ]
    };

    const options = {
      method: 'POST',
      url,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${config.apiKey}`
      },
      body: JSON.stringify(payload),
      json: false,
      timeout: 60000
    };

    const responseStr = await request(options);
    const response = JSON.parse(responseStr);

    const text = response?.choices?.[0]?.message?.content || '';
    if (text) {
      return {
        code: 0,
        msg: '识别成功',
        data: { text },
        sid: response.id || ''
      };
    }
    return { code: -2, msg: '识别失败', data: null, sid: response.id || '' };
  } catch (err) {
    console.error('aliyunOcr error', err);
    return { code: -3, msg: err.message || '云函数执行错误', data: null };
  }
}; 