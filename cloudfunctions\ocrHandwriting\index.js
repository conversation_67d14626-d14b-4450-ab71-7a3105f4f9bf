// 手写文字识别云函数
// 基于讯飞开放平台API
// 参考文档: https://www.xfyun.cn/doc/words/wordRecg/API.html

const cloud = require('wx-server-sdk');
const crypto = require('crypto');
const request = require('request-promise');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// 系统配置
const config = {
  // OCR手写文字识别服务webapi接口地址
  hostUrl: "https://webapi.xfyun.cn/v1/service/v1/ocr/handwriting",
  host: "webapi.xfyun.cn",
  // 在控制台-我的应用-手写文字识别获取
  appid: "0ead112d",
  // 接口密钥(webapi类型应用开通手写文字识别后，控制台--我的应用---手写文字识别---相应服务的apikey)
  apiKey: "edeca16f510b3fdff669f62e2514f449",
  uri: "/v1/ise"
};

/**
 * 云函数入口函数
 * @param {Object} event 云函数触发事件
 * @param {string} event.image 图片的base64编码字符串（不含前缀）
 * @param {string} [event.language='cn'] 识别语言，默认中文
 */
exports.main = async (event, context) => {
  try {
    // 获取参数
    const { image, language = 'cn' } = event;

    if (!image) {
      return {
        code: -1,
        msg: '缺少图片数据',
        data: null
      };
    }

    console.log('开始识别手写文字，语言:', language);


    // 获取当前时间戳
    const ts = Math.floor(Date.now() / 1000);
    // 对base64图片数据进行urlencode处理
    const urlEncodedImage = encodeURIComponent(image);
    // 组装业务参数
    const xParam = {
      language: language// 'cn'中文，'en'英文
    };
    const xParamStr = Buffer.from(JSON.stringify(xParam)).toString('base64');

    // 计算校验和
    const xCheckSum = crypto.createHash('md5')
      .update(config.apiKey + ts + xParamStr)
      .digest('hex');

    // 组装请求头
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
      'X-Appid': config.appid,
      'X-CurTime': ts + "",
      'X-Param': xParamStr,
      'X-CheckSum': xCheckSum
    };
     
    let options = {
      method: 'POST',
      url: config.hostUrl,
      headers: headers,
      form: {
        image:image
      },
      json: true 
    }
    console.log('headers:',options);
    // 发送请求
    const response = await request(options);

    console.log('识别结果:', response);

    // 返回结果
    if (response.code  == 0) {
      return {
        code: 0,
        msg: '识别成功',
        data: response.data,
        sid: response.sid
      };
    } else {

      return {
        code: response.code,
        msg: `识别失败: ${response.desc}`,
        sid: response.sid,
        data: null
      };
    }
  } catch (error) {
    console.error('手写识别云函数执行错误:', error);
    return {
      code: -2,
      msg: '云函数执行错误: ' + error.message,
      data: null
    };
  }
}; 