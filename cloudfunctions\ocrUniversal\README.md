# 通用文字识别云函数

基于讯飞开放平台的通用文字识别API，支持中英文印刷体和手写体识别。

## 功能特点

- 支持中英文混合识别
- 支持印刷体和手写体
- 在倾斜文字上效果有提升
- 支持部分生僻字的识别
- 图像数据base64编码后大小不得超过10M

## 使用方法

### 调用云函数

```javascript
// 小程序端调用
wx.cloud.callFunction({
  name: 'ocrUniversal',
  data: {
    image: 'base64编码的图片数据', // 必须，不含data:image/jpeg;base64,前缀
    category: 'ch_en_public_cloud'  // 可选，识别类别，默认中英文公有云
  },
  success: res => {
    console.log('识别结果:', res.result);
    if (res.result.code === 0) {
      console.log('识别文字:', res.result.data.text);
    } else {
      console.error('识别失败:', res.result.msg);
    }
  },
  fail: err => {
    console.error('调用失败:', err);
  }
});
```

### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| image | string | 是 | 图片的base64编码字符串（不含前缀） |
| category | string | 否 | 识别类别，默认'ch_en_public_cloud' |

### 返回结果

```javascript
{
  code: 0,                    // 状态码，0表示成功
  msg: '识别成功',             // 状态消息
  data: {
    text: '识别的文字内容',     // 清理后的文字（去除空格换行）
    originalText: '原始文字',   // 原始识别文字
    recognizedWords: [         // 识别出的单字数组
      {
        text: '单字内容',      // 单个字符
        confidence: 0.98,      // 置信度，0-1之间
        rect: {                // 字符位置信息
          left: 23,
          top: 7,
          width: 24,
          height: 32
        }
      }
      // 更多单字...
    ],
    rawResponse: {}            // 解析后的API响应数据
  },
  sid: 'session_id'          // 会话ID
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| -1 | 缺少图片数据 |
| -2 | 云函数执行错误 |
| -3 | 响应数据格式异常 |
| -4 | 解析JSON结果失败 |
| 其他 | 讯飞API返回的错误码 |

## 部署方法

1. 在微信开发者工具中右键点击`cloudfunctions/ocrUniversal`目录
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成 