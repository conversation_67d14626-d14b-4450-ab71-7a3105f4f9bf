.selected-words-bar {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 16rpx 0 8rpx 0;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}
.selected-words-scroll {
  white-space: nowrap;
  overflow-x: auto;
  height: 60rpx;
  padding: 0 24rpx;
}
.selected-words-actions {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  margin-top: 8rpx;
  margin-bottom: 8rpx;
}
.selected-word-item {
  display: inline-flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 24rpx;
  padding: 0 20rpx;
  margin-right: 12rpx;
  font-size: 28rpx;
  position: relative;
  height: 48rpx;
}
.remove-icon {
  width: 28rpx;
  height: 28rpx;
  margin-left: 6rpx;
}
.clear-btn, .start-btn {
  height: 48rpx;
  line-height: 48rpx;
  font-size: 26rpx;
  border-radius: 24rpx;
  padding: 0 20rpx;
}
.clear-btn {
  background: #f8d7da;
  color: #a94442;
  border: none;
}
.start-btn {
  background: #4caf50;
  color: #fff;
  border: none;
}
.course-list-scroll {
  height: calc(100vh - 80rpx);
  background: #fafbfc;
  padding-bottom: 40rpx;
}
.course-item {
  margin-bottom: 12rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx #f0f1f2;
}
.course-title {
  font-size: 30rpx;
  font-weight: bold;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.arrow {
  font-size: 28rpx;
  margin-left: 8rpx;
}
.word-list {
  display: flex;
  flex-wrap: wrap;
  padding: 12rpx 24rpx 20rpx 24rpx;
}
.word-btn {
  background: #f0f0f0;
  border-radius: 20rpx;
  padding: 8rpx 20rpx;
  margin: 8rpx 12rpx 0 0;
  font-size: 28rpx;
  color: #333;
  transition: background 0.2s;
}
.word-btn.selected {
  background: #4caf50;
  color: #fff;
} 