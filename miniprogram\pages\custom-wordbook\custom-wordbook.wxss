/* 顶部已选字词区域 */
.selected-words-bar {
  background: linear-gradient(to bottom, #594995 0%, var(--primary-light) 100%);
  padding: 32rpx 24rpx;
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(89, 73, 149, 0.2);
}

.selected-words-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.selected-words-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

.selected-words-count {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.selected-words-scroll {
  height: 160rpx;
  margin-bottom: 24rpx;
}

.selected-words-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 0 8rpx;
  min-height: 160rpx;
}

.selected-word-item {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8rpx;
  padding: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-width: 120rpx;
  flex-shrink: 0;
}

.selected-word-item:active {
  transform: scale(0.95);
}

.remove-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

.remove-icon {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

.empty-selected {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120rpx;
}

.empty-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 28rpx;
}

.selected-words-actions {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.action-btn {
  flex: 1;
  max-width: 200rpx;
  height: 88rpx;
  border-radius: 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
}

.action-btn::after {
  border: none;
}

.clear-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.clear-btn:not([disabled]):active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.98);
}

.clear-btn[disabled] {
  opacity: 0.5;
}

.start-btn {
  background: white;
  color: #667eea;
  box-shadow: 0 6rpx 20rpx rgba(255, 255, 255, 0.3);
}

.start-btn:not([disabled]):active {
  background: #f5f7fa;
  transform: scale(0.98);
}

.start-btn[disabled] {
  opacity: 0.5;
  background: rgba(255, 255, 255, 0.5);
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* 田字格样式 */
.word-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.character-grid {
  display: flex;
  gap: 8rpx;
}

.tianzige {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 田字格十字线 */
.tianzige::before,
.tianzige::after {
  content: '';
  position: absolute;
  background-color: #e0e0e0;
  transition: all 0.3s ease;
}

.tianzige::before {
  width: 1rpx;
  height: 100%;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

.tianzige::after {
  width: 100%;
  height: 1rpx;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.tianzige-inner {
  font-size: 48rpx;
  font-weight: 500;
  color: #2c3e50;
  z-index: 1;
  position: relative;
  transition: all 0.3s ease;
}

/* 已选字词区域的田字格样式调整 */
.selected-word-item .tianzige {
  width: 60rpx;
  height: 60rpx;
  border-color: #ccc;
  background: #f8f9fa;
}

.selected-word-item .tianzige-inner {
  font-size: 36rpx;
  color: #495057;
}

/* 字词选择区域的田字格样式调整 */
.word-item .tianzige {
  width: 80rpx;
  height: 80rpx;
}

.word-item .tianzige-inner {
  font-size: 48rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .word-grid {
    gap: 12rpx;
  }

  .word-item {
    min-height: 100rpx;
    padding: 12rpx;
  }

  .word-item .tianzige {
    width: 70rpx;
    height: 70rpx;
  }

  .word-item .tianzige-inner {
    font-size: 42rpx;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.word-item {
  animation: fadeInUp 0.3s ease-out;
}

.word-check {
  animation: bounceIn 0.5s ease-out;
}

/* 页面整体样式 */
page {
  background: #f8f9fa;
  height: 100vh;
}

/* 滚动条样式优化 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
/* 课程列表区域 */
.course-list-scroll {
  height: calc(100vh - 280rpx);
  background: #f8f9fa;
  padding: 24rpx;
  padding-bottom: 40rpx;
}

.course-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.course-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
}

.course-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  cursor: pointer;
  transition: all 0.3s ease;
}

.course-header:active {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.course-title-content {
  flex: 1;
}

.course-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.course-count {
  font-size: 24rpx;
  color: #7f8c8d;
}

.course-arrow {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.course-arrow.expanded {
  transform: rotate(90deg);
  background: rgba(102, 126, 234, 0.2);
}

.arrow-icon {
  font-size: 24rpx;
  color: #667eea;
  font-weight: bold;
}

/* 字词网格 */
.word-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
  gap: 16rpx;
  background: #fafbfc;
}

.word-item {
  position: relative;
  background: white;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 120rpx;
  cursor: pointer;
}

.word-item:active {
  transform: scale(0.98);
}

.word-item.selected {
  border-color: #594995;
  background: #594995;
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(89, 73, 149, 0.3);
}

.word-item.selected .tianzige {
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

.word-item.selected .tianzige::before,
.word-item.selected .tianzige::after {
  background-color: rgba(255, 255, 255, 0.3);
}

.word-item.selected .tianzige-inner {
  color: white;
}

.word-check {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #4caf50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}