// 通用文字识别云函数
// 基于讯飞开放平台API - 通用文字识别
// 参考文档: https://www.xfyun.cn/doc/words/wordRecg/API.html

const cloud = require('wx-server-sdk');
const crypto = require('crypto');
const request = require('request-promise');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// 系统配置
const config = {
  // 讯飞开放平台应用配置
  APPId: "0ead112d",  // 控制台获取
  APISecret: "YjM4MzU5OTFjZjQ1ODM1OTY2ZDUyZDVj",  // 控制台获取
  APIKey: "95654fe0d6b1868a3cd8b8642db4e11c",  // 控制台获取
  // API地址
  url: 'https://api.xf-yun.com/v1/private/sf8e6aca1'
};

/**
 * 构建WebSocket认证URL
 * @param {string} requestUrl 请求URL
 * @param {string} method HTTP方法
 * @param {string} apiKey API密钥
 * @param {string} apiSecret API密钥
 * @returns {string} 认证后的URL
 */
function assembleAuthUrl(requestUrl, method = "POST", apiKey, apiSecret) {
  try {
    const url = new URL(requestUrl);
    const host = url.hostname;
    const path = url.pathname;
    
    const now = new Date();
    const date = now.toUTCString();
    
    const signatureOrigin = `host: ${host}\ndate: ${date}\n${method} ${path} HTTP/1.1`;
    
    const signatureSha = crypto
      .createHmac('sha256', apiSecret)
      .update(signatureOrigin)
      .digest('base64');
    
    const authorizationOrigin = `api_key="${apiKey}", algorithm="hmac-sha256", headers="host date request-line", signature="${signatureSha}"`;
    const authorization = Buffer.from(authorizationOrigin).toString('base64');
    
    const params = new URLSearchParams({
      host: host,
      date: date,
      authorization: authorization
    });
    
    return `${requestUrl}?${params.toString()}`;
  } catch (error) {
    throw new Error(`构建认证URL失败: ${error.message}`);
  }
}

/**
 * 云函数入口函数
 * @param {Object} event 云函数触发事件
 * @param {string} event.image 图片的base64编码字符串（不含前缀data:image/jpeg;base64,）
 * @param {string} [event.category='ch_en_public_cloud'] 识别类别，默认中英文公有云
 */
exports.main = async (event, context) => {
  try {
    // 获取参数
    const { image, category = 'ch_en_public_cloud' } = event;

    if (!image) {
      return {
        code: -1,
        msg: '缺少图片数据',
        data: null
      };
    }

    console.log('开始通用文字识别，类别:', category);

    // 构建请求体
    const body = {
      "header": {
        "app_id": config.APPId,
        "status": 3
      },
      "parameter": {
        "sf8e6aca1": {
          "category": category,  // ch_en_public_cloud: 中英文公有云
          "result": {
            "encoding": "utf8",
            "compress": "raw",
            "format": "json"
          }
        }
      },
      "payload": {
        "sf8e6aca1_data_1": {
          "encoding": "jpg",
          "image": image,
          "status": 3
        }
      }
    };

    // 构建认证URL
    const requestUrl = assembleAuthUrl(config.url, "POST", config.APIKey, config.APISecret);
    
    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      'Host': 'api.xf-yun.com',
      'app_id': config.APPId
    };

    // 发送请求
    const options = {
      method: 'POST',
      url: requestUrl,
      headers: headers,
      body: JSON.stringify(body),
      json: false // 因为响应需要手动解析
    };

    console.log('发送识别请求...');
    const response = await request(options);
    
    // 解析响应
    const responseData = JSON.parse(response);
    console.log('识别原始结果:', responseData);

    // 检查响应状态
    if (responseData.header && responseData.header.code === 0) {
      // 解码结果
      if (responseData.payload && responseData.payload.result && responseData.payload.result.text) {
        const encodedText = responseData.payload.result.text;
        const decodedText = Buffer.from(encodedText, 'base64').toString('utf8');
        
        // 解析JSON格式的结果
        let parsedResult;
        try {
          parsedResult = JSON.parse(decodedText);
          console.log('解析后的结果:', parsedResult);
        } catch (error) {
          console.error('解析JSON结果失败:', error);
          return {
            code: -4,
            msg: '解析JSON结果失败: ' + error.message,
            data: null,
            sid: responseData.header.sid || ''
          };
        }
        
        // 提取识别的文字
        const recognizedWords = [];
        let fullText = '';
        
        // 处理新的API返回格式
        if (parsedResult.pages && Array.isArray(parsedResult.pages)) {
          parsedResult.pages.forEach(page => {
            if (page.lines && Array.isArray(page.lines)) {
              page.lines.forEach(line => {
                // 处理单字级别的识别结果
                if (line.word_units && Array.isArray(line.word_units)) {
                  line.word_units.forEach(unit => {
                    if (unit.content) {
                      recognizedWords.push({
                        text: unit.content,
                        confidence: unit.conf || 0,
                        rect: {
                          left: unit.coord ? unit.coord[0].x : 0,
                          top: unit.coord ? unit.coord[0].y : 0,
                          width: unit.coord ? (unit.coord[1].x - unit.coord[0].x) : 0,
                          height: unit.coord ? (unit.coord[2].y - unit.coord[1].y) : 0
                        }
                      });
                      fullText += unit.content;
                    }
                  });
                }
                // 如果没有单字级别的结果，则使用单词级别的结果
                else if (line.words && Array.isArray(line.words)) {
                  line.words.forEach(word => {
                    if (word.content) {
                      // 对于中文，每个字符都应该是一个独立的识别结果
                      const chars = word.content.split('');
                      chars.forEach(char => {
                        recognizedWords.push({
                          text: char,
                          confidence: word.conf || 0,
                          rect: word.coord ? {
                            left: word.coord[0].x,
                            top: word.coord[0].y,
                            width: (word.coord[1].x - word.coord[0].x) / chars.length, // 简单平均分配宽度
                            height: word.coord[2].y - word.coord[1].y
                          } : null
                        });
                      });
                      fullText += word.content;
                    }
                  });
                }
              });
            }
          });
        }
        
        // 清理文本（去除空格、换行、制表符）
        const finalResult = fullText.replace(/\s+/g, '').trim();
        
        console.log('识别成功，结果:', finalResult);
        console.log('识别出的文字数组:', recognizedWords);
        
        return {
          code: 0,
          msg: '识别成功',
          data: {
            text: finalResult,
            originalText: decodedText,
            recognizedWords: recognizedWords, // 添加单字级别的识别结果
            rawResponse: parsedResult
          },
          sid: responseData.header.sid || ''
        };
      } else {
        return {
          code: -3,
          msg: '响应数据格式异常',
          data: null,
          sid: responseData.header.sid || ''
        };
      }
    } else {
      const errorCode = responseData.header ? responseData.header.code : -1;
      const errorMsg = responseData.header ? responseData.header.message : '未知错误';
      
      return {
        code: errorCode,
        msg: `识别失败: ${errorMsg}`,
        data: null,
        sid: responseData.header ? responseData.header.sid : ''
      };
    }

  } catch (error) {
    console.error('通用文字识别云函数执行错误:', error);
    return {
      code: -2,
      msg: '云函数执行错误: ' + error.message,
      data: null
    };
  }
}; 