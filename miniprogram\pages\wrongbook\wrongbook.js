const app = getApp();
import createHanziWriterContext from 'hanzi-writer-miniprogram';
const SimpleStorage = require('../../utils/simpleStorage.js');
Page({
  data: {
    // 错题数据
    pendingErrorsByCourse: [], // 待处理错题（按课程分组）
    historyErrorsByCourse: [], // 历史错题（按课程分组）

    // 标签页
    activeTab: 0, // 0: 待处理，1: 历史记录

    // 选择状态
    selectedWords: [], // 选中的待处理错题
    allSelected: false,

    // 统计信息
    totalPendingErrors: 0,
    totalHistoryErrors: 0,

    // 随机挑战配置
    randomChallengeWordCount: 5, // 默认随机挑战字数

    // 刷新状态
    refreshing: false
    // 注意：移除了showStrokeModal、currentCharacter、currentCharacterIndex、currentWord、isAutoPlaying等与弹窗相关的属性
  },

  onLoad() {
    // console.log('错题集页面加载');
    // 初始化重试计数器
    this._initRetryCount = 0;
    this._animationRetryCount = 0;

    // 初始化Canvas修补和错误处理
    this.patchCanvasAPI();
    this.initGlobalErrorHandling();

    // 添加调试信息
    // console.log('检查hanzi-writer-miniprogram是否正确导入:', typeof createHanziWriterContext);

    // 检查wordbg.jpg图片是否存在
    wx.getImageInfo({
      src: '/images/wordbg.jpg',
      success: (res) => {
        // console.log('田字格背景图片加载成功:', res);
      },
      fail: (err) => {
        // console.error('田字格背景图片加载失败:', err);
        wx.showToast({
          title: '田字格背景图片缺失',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  onShow() {
    console.log('错题集页面显示');

    // 检查是否需要切换到历史记录tab（从每日任务跳转）
    if (SimpleStorage.shouldSwitchToHistoryTab()) {
      console.log('检测到需要切换到历史记录tab');
      // 切换到历史记录tab
      this.setData({ activeTab: 1 });
    }

    // 每次显示页面时重新加载错题数据，确保按当前教材筛选
    this.loadErrorWords();

    // 检查是否有错题复习结果
    this.checkErrorReviewResult();

    // **新增：更新错题集徽章**
    try {
      // **修改：检查是否需要强制更新徽章**
      if (app && app.updateTabBarBadgeDelayed) {
        if (app.globalData.badgeNeedsUpdate) {
          console.log('🔴 检测到徽章需要更新标记，立即更新徽章');
          app.globalData.badgeNeedsUpdate = false;
          app.updateTabBarBadge(); // 直接更新，不延迟
        } else {
          app.updateTabBarBadgeDelayed(); // 正常延迟更新
        }
        console.log('📍 错题集页面显示时更新TabBar徽章');
      }
    } catch (badgeError) {
      console.error('更新TabBar徽章失败:', badgeError);
    }
  },

  // 页面初次渲染完成
  onReady() {
    console.log('页面渲染完成');
    // 页面渲染完成后，预初始化 hanzi-writer 组件（但不显示）
    setTimeout(() => {
      this.preInitializeHanziWriter();
    }, 500);
  },

  onUnload() {
    console.log('错题集页面卸载');
    // 清理汉字书写器实例
    if (this._writerCtx) {
      try {
        if (this._writerCtx.destroy) {
          this._writerCtx.destroy();
        }
      } catch (e) {
        console.warn('页面卸载时清理汉字书写器实例失败:', e);
      }
      this._writerCtx = null;
    }
    this._isInitializing = false;
  },

  onHide() {
    console.log('错题集页面隐藏');
    // 如果弹窗打开，关闭它
    if (this.data.showStrokeModal) {
      this.onCloseStrokeModal();
    }
  },

  // **新增：从对象格式的courseInfo构建课程标题（原有逻辑）**
  buildCourseTitleFromObject(courseInfo) {
    // 构建课程标题 - 优先使用完整信息，并添加课程序号
    if (courseInfo.courseName) {
      // 如果有完整的课程标题，直接使用
      if (courseInfo.courseTitle) {
        return courseInfo.courseTitle;
      } else {
        // 否则构建标题
        const publisher = courseInfo.publisher || '';
        const grade = courseInfo.grade || '';
        const term = courseInfo.term === 'term1' ? '上册' :
          courseInfo.term === 'term2' ? '下册' :
            courseInfo.term || '';

        // 提取课程序号（从courseName中提取"第X课"信息）
        let lessonNumber = '';
        const lessonMatch = courseInfo.courseName.match(/第(\d+|[一二三四五六七八九十]+)课/);
        if (lessonMatch) {
          lessonNumber = `第${lessonMatch[1]}课：`;
        }

        // 只有当有具体信息时才构建完整标题
        if (publisher && grade) {
          return `${publisher} ${grade}${term} - ${lessonNumber}${courseInfo.courseName}`;
        } else if (courseInfo.courseName) {
          // 如果没有出版社和年级信息，只显示课程名称（包含序号）
          return lessonNumber ? `${lessonNumber}${courseInfo.courseName}` : courseInfo.courseName;
        }
      }
    }

    // 如果没有课程名称，尝试使用课程ID或显示未知
    return courseInfo.courseId || '未知课程';
  },

  // **新增：根据courseId查找课程标题**
  findCourseTitleById(courseId, publisherId = 'renjiaoban') {
    try {
      let config = null;

      try {
        if (publisherId === 'renjiaoban') {
          config = require('../../config/renjiaoban.js');
        } else if (publisherId === 'beishidaban') {
          config = require('../../config/beishidaban.js');
        } else if (publisherId === 'sujiaoba') {
          config = require('../../config/sujiaoba.js');
        }
      } catch (configError) {
        console.log(`无法加载配置文件 ${publisherId}:`, configError);
        return null;
      }

      if (!config || !config.grades) {
        console.log(`配置文件格式错误或为空: ${publisherId}`);
        return null;
      }

      // 遍历所有年级、学期、课程查找匹配的courseId
      for (const gradeKey in config.grades) {
        const grade = config.grades[gradeKey];
        if (!grade.terms) continue;

        for (const termKey in grade.terms) {
          const term = grade.terms[termKey];
          if (!term.lessons) continue;

          for (const lesson of term.lessons) {
            if (lesson.id === courseId) {
              // 构建完整的课程标题
              const gradeName = grade.name || gradeKey;
              const termName = term.name || termKey;
              const unitPrefix = lesson.unit ? `${lesson.unit} ` : '';
              const numPrefix = lesson.num ? `${lesson.num}：` : '';
              const title = lesson.title || lesson.name || courseId;

              const fullTitle = `${config.name} ${gradeName}${termName} - ${unitPrefix}${numPrefix}${title}`;


              return {
                fullTitle,
                shortTitle: `${numPrefix}${title}`,
                gradeInfo: `${config.name} ${gradeName}${termName}`,
                unit: lesson.unit,
                num: lesson.num,
                title: lesson.title || lesson.name
              };
            }
          }
        }
      }

      console.log(`❌ 未找到课程ID "${courseId}" 的标题`);
      return null;
    } catch (error) {
      console.error(`查找课程标题时出错:`, error);
      return null;
    }
  },

  // **新增：从课程配置中查找拼音**
  findPinyinFromConfig(word, courseInfo) {
    try {
      // 加载对应出版社的课程配置
      const publisherId = courseInfo?.publisherId || 'renjiaoban';
      let config = null;

      try {
        if (publisherId === 'renjiaoban') {
          config = require('../../config/renjiaoban.js');
        } else if (publisherId === 'beishidaban') {
          config = require('../../config/beishidaban.js');
        } else if (publisherId === 'sujiaoba') {
          config = require('../../config/sujiaoba.js');
        }
      } catch (configError) {
        console.log(`无法加载配置文件 ${publisherId}:`, configError);
        return null;
      }

      if (!config || !config.grades) {
        console.log(`配置文件格式错误或为空: ${publisherId}`);
        return null;
      }

      // 遍历所有年级、学期、课程查找字词
      for (const gradeKey in config.grades) {
        const grade = config.grades[gradeKey];
        if (!grade.terms) continue;

        for (const termKey in grade.terms) {
          const term = grade.terms[termKey];
          if (!term.lessons) continue;

          for (const lesson of term.lessons) {
            if (!lesson.tables) continue;

            // 在各个表中查找字词
            for (const tableType of ['shiZiBiao', 'xieZiBiao', 'ciYuBiao']) {
              const table = lesson.tables[tableType];
              if (!table || !Array.isArray(table)) continue;

              for (const item of table) {
                if (item.word === word && item.pinyin) {
                  console.log(`✅ 在课程配置中找到字词 "${word}" 的拼音:`, item.pinyin);
                  return item.pinyin;
                }
              }
            }
          }
        }
      }

      console.log(`❌ 在课程配置中未找到字词 "${word}" 的拼音`);
      return null;
    } catch (error) {
      console.error(`查找拼音时出错:`, error);
      return null;
    }
  },

  // **新增：为错题补充拼音数据**
  enrichErrorWordsWithPinyin(errorWords) {
    console.log('📚 开始为错题补充拼音数据...');
    let enrichedCount = 0;
    let totalEmpty = 0;

    const enrichedWords = errorWords.map(error => {
      // 检查是否需要补充拼音
      const needsPinyin = !error.pinyin || error.pinyin.trim() === '';

      if (needsPinyin) {
        totalEmpty++;
        // 从课程配置中查找拼音
        const foundPinyin = this.findPinyinFromConfig(error.word, error.courseInfo);

        if (foundPinyin) {
          enrichedCount++;
          console.log(`🔧 为字词 "${error.word}" 补充拼音: ${foundPinyin}`);
          return {
            ...error,
            pinyin: foundPinyin,
            enriched: true // 标记为已补充
          };
        } else {
          console.log(`⚠️  字词 "${error.word}" 在课程配置中未找到拼音`);
        }
      }

      return error;
    });

    console.log(`📊 拼音补充统计: 空拼音错题 ${totalEmpty} 个，成功补充 ${enrichedCount} 个`);

    // 如果有补充，保存更新后的错题数据
    if (enrichedCount > 0) {
      SimpleStorage.setErrorWords(enrichedWords);
      wx.showToast({
        title: `已为${enrichedCount}个错题补充拼音`,
        icon: 'success',
        duration: 2000
      });
    }

    return enrichedWords;
  },

  // 加载错题数据
  loadErrorWords() {
    const errorWords = SimpleStorage.getErrorWords();


    // **新增：为错题补充拼音数据**
    const enrichedErrorWords = this.enrichErrorWordsWithPinyin(errorWords);



    // 获取当前选择的教材信息
    const currentTextbook = SimpleStorage.getCurrentTextbook();
    console.log('当前选择的教材信息:', currentTextbook);

    // 筛选当前教材版本的错题
    const filteredErrorWords = enrichedErrorWords.filter(error => {
      // 如果没有选择教材或错题没有课程信息，则显示所有错题
      if (!currentTextbook.publisher || !error.courseInfo) return true;

      // **新增：优先使用 textbookKey 进行教材匹配**
      if (typeof error.courseInfo === 'object' && error.courseInfo.textbookKey) {
        const currentTextbookKey = SimpleStorage.getTextbookKey(currentTextbook);
        const isCurrentTextbook = error.courseInfo.textbookKey === currentTextbookKey;
        //console.log(`[错题过滤] 使用textbookKey匹配: ${error.word} -> ${error.courseInfo.textbookKey} vs ${currentTextbookKey} = ${isCurrentTextbook}`);
        return isCurrentTextbook;
      }


      //   console.log(`[错题过滤] 兜底显示: ${error.word}`);
      return true; // 兜底显示所有
    });

    console.log('筛选后的错题数据总数:', filteredErrorWords.length);

    // 分析每个错题的状态
    const correctedErrors = [];
    const uncorrectedErrors = [];

    filteredErrorWords.forEach((error, index) => {

      if (error.corrected === true) {
        correctedErrors.push(error);
      } else {
        uncorrectedErrors.push(error);
      }
    });

    console.log(`\n状态统计:`);
    console.log(`  已订正: ${correctedErrors.length} 个`);
    console.log(`  未订正: ${uncorrectedErrors.length} 个`);

    // 重新定义分类逻辑
    // 待处理：只显示未订正的错题
    const pendingErrors = filteredErrorWords.filter(error => !error.corrected || error.corrected === false);
    // 历史记录：显示所有错题（包括已订正和未订正）
    const historyErrors = [...filteredErrorWords]; // 显示所有错题

    console.log('\n=== 分类结果 ===');
    console.log('待处理错题（未订正）:', pendingErrors.length, '个');
    console.log('待处理错题详情:', pendingErrors.map(e => `${e.word}(${e.corrected ? '已订正' : '未订正'})`));
    console.log('历史错题（所有）:', historyErrors.length, '个');

    // 按课程分组
    const pendingErrorsByCourse = this.groupErrorsByCourse(pendingErrors);
    const historyErrorsByCourse = this.groupErrorsByCourse(historyErrors);

    //console.log('\n=== 按课程分组结果 ===');
    //console.log('待处理错题（按课程）:', pendingErrorsByCourse.length, '个课程');
    pendingErrorsByCourse.forEach((course, index) => {
      //console.log(`  课程 ${index + 1}: ${course.courseName} - ${course.errorCount} 个错题`);
    });


    historyErrorsByCourse.forEach((course, index) => {
      //console.log(`  课程 ${index + 1}: ${course.courseName} - ${course.errorCount} 个错题`);
    });

    this.setData({
      pendingErrorsByCourse,
      historyErrorsByCourse,
      totalPendingErrors: pendingErrors.length,
      totalHistoryErrors: historyErrors.length,
      selectedWords: [], // 重置选择
      allSelected: false,
      // 更新随机挑战字数，确保不超过历史错题总数和36的最小值
      randomChallengeWordCount: Math.min(this.data.randomChallengeWordCount, Math.min(36, Math.max(1, historyErrors.length)))
    });


  },

  // 按课程分组错题
  groupErrorsByCourse(errorWords) {
    const groups = {};

    errorWords.forEach(error => {
      const courseInfo = error.courseInfo;
      let courseKey = 'unknown_course';
      let courseName = '未知课程';

      if (courseInfo) {
        // **兼容性处理：支持新旧格式的courseInfo**
        if (typeof courseInfo === 'string') {
          // 新格式：courseInfo是字符串courseId
          courseKey = courseInfo;

          // **修复：根据courseId查找实际的课程标题**
          // 尝试从当前教材信息中获取publisherId
          const currentTextbook = SimpleStorage.getCurrentTextbook();
          const publisherId = currentTextbook?.publisherId || 'renjiaoban';

          console.log(`🔍 查找课程标题: courseId="${courseInfo}", publisherId="${publisherId}"`);

          const courseDetails = this.findCourseTitleById(courseInfo, publisherId);
          if (courseDetails) {
            courseName = courseDetails.title; // 只显示课程标题，如"天地人"
            console.log(`✅ 成功获取课程标题: "${courseName}"`);
          } else {
            courseName = `课程 ${courseInfo}`; // 备用显示
            console.log(`⚠️  未找到课程标题，使用备用显示: "${courseName}"`);
          }
        } else if (typeof courseInfo === 'object') {
          // 旧格式：courseInfo是对象
          // 优先使用courseId构建key
          if (courseInfo.courseId) {
            courseKey = courseInfo.courseId;

            // **优化：如果有courseId，优先使用新的查找方法**
            const publisherId = courseInfo.publisherId || 'renjiaoban';
            const courseDetails = this.findCourseTitleById(courseInfo.courseId, publisherId);

            if (courseDetails) {
              courseName = courseDetails.title; // 只显示课程标题，如"天地人"
            } else {
              // 如果查找失败，使用原有的构建逻辑
              courseName = this.buildCourseTitleFromObject(courseInfo);
            }
          } else {
            // 备用方案：使用组合ID
            const publisherId = courseInfo.publisherId || 'unknown';
            const gradeId = courseInfo.gradeId || 'unknown';
            const term = courseInfo.term || 'unknown';
            courseKey = `${publisherId}_${gradeId}_${term}`;

            // 使用原有的构建逻辑
            courseName = this.buildCourseTitleFromObject(courseInfo);
          }
        }
      }

      if (!groups[courseKey]) {
        groups[courseKey] = {
          courseKey,
          courseName,
          courseInfo,
          words: [],
          errorCount: 0,
          allSelected: false
        };
      }

      // 确保字词数据完整性并处理拼音格式
      let formattedPinyin = error.pinyin || '';



      // 处理拼音格式：确保每个字对应一个拼音，用空格分隔
      if (error.word && error.word.length > 0) {
        if (formattedPinyin) {
          // 检查原始拼音格式
          let pinyinParts = formattedPinyin.trim().split(/\s+/); // 使用正则分割多个空格
          const wordLength = error.word.length;


          // 如果拼音部分数量与字数不匹配，尝试智能分割
          if (pinyinParts.length !== wordLength) {
            if (pinyinParts.length === 1 && wordLength > 1) {
              // 单个拼音字符串，尝试按拼音规则分割
              const singlePinyin = pinyinParts[0];
              pinyinParts = this.splitPinyinString(singlePinyin, wordLength);
              console.log(`智能分割后的拼音:`, pinyinParts);
            }

            // 如果分割后仍然不匹配，进行长度调整
            if (pinyinParts.length < wordLength) {
              // 补充缺少的拼音部分
              const additionalParts = Array(wordLength - pinyinParts.length).fill('');
              pinyinParts = [...pinyinParts, ...additionalParts];
            } else if (pinyinParts.length > wordLength) {
              // 如果拼音过多，只取前面对应数量的拼音
              pinyinParts = pinyinParts.slice(0, wordLength);
            }
          }

          // 重新组合拼音
          formattedPinyin = pinyinParts.join(' ');
        } else {
          // 如果没有拼音，为每个字符创建空的拼音
          formattedPinyin = Array(error.word.length).fill('').join(' ');
        }
      }



      const pinyinArray = formattedPinyin ? formattedPinyin.split(' ') : [];


      const wordData = {
        ...error,
        word: error.word || '未知字词',
        pinyin: formattedPinyin,
        pinyinArray: pinyinArray, // 使用已处理的拼音数组
        selected: false,
        attempts: error.attempts || 1,
        // 格式化时间显示
        lastErrorTime: this.formatTimeAgo(error.lastErrorTime),
        correctedTime: error.correctedTime ? this.formatTimeAgo(error.correctedTime) : null
      };

      groups[courseKey].words.push(wordData);
      groups[courseKey].errorCount++;
    });

    // 按课程分组后的排序处理
    const groupsArray = Object.values(groups);

    // 对每个课程组内的错题进行排序：已订正的排在前面
    groupsArray.forEach(group => {
      group.words.sort((a, b) => {
        // 已订正的排前面，未订正的排后面
        if (a.corrected !== b.corrected) {
          return a.corrected ? -1 : 1;
        }
        // 同样订正状态下，按时间排序（最新的在前）
        const timeA = new Date(a.correctedTime || a.lastErrorTime || a.timestamp || 0);
        const timeB = new Date(b.correctedTime || b.lastErrorTime || b.timestamp || 0);
        return timeB - timeA;
      });
    });

    // 按课程的最新错题时间排序
    return groupsArray.sort((a, b) =>
      new Date(b.words[0]?.timestamp || 0) - new Date(a.words[0]?.timestamp || 0)
    );
  },

  // 格式化时间显示
  formatTimeAgo(timeString) {
    if (!timeString) return '未知时间';

    const now = new Date();
    const time = new Date(timeString);
    const diffMs = now - time;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 0) {
      return `${diffDays}天前`;
    } else if (diffHours > 0) {
      return `${diffHours}小时前`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes}分钟前`;
    } else {
      return '刚刚';
    }
  },

  // 切换标签页
  onTabChange(e) {
    const activeTab = parseInt(e.currentTarget.dataset.index);

    // 切换到待处理标签页时，清空所有选择状态
    if (activeTab === 0) {
      // 重置待处理错题的选择状态
      const { pendingErrorsByCourse } = this.data;
      pendingErrorsByCourse.forEach(course => {
        course.allSelected = false;
        course.words.forEach(word => {
          word.selected = false;
        });
      });

      this.setData({
        activeTab,
        selectedWords: [],
        allSelected: false,
        pendingErrorsByCourse
      });
    } else {
      // 切换到历史记录标签页时，只需要清空选择状态，不需要操作课程数据
      this.setData({
        activeTab,
        selectedWords: [],
        allSelected: false
      });
    }

    console.log(`切换到标签页 ${activeTab}, 已清空选择状态`);
  },

  // 切换课程全选状态
  onToggleCourseSelect(e) {
    const { courseIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex < 0 || courseIndex >= pendingErrorsByCourse.length) return;

    const course = pendingErrorsByCourse[courseIndex];
    const newSelectState = !course.allSelected;

    // 更新课程选择状态
    course.allSelected = newSelectState;
    course.words.forEach(word => {
      word.selected = newSelectState;
    });

    // 更新选中列表
    this.updateSelectedWords();

    this.setData({ pendingErrorsByCourse });
  },

  // 切换单个字词选择状态
  onToggleWordSelect(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex < 0 || courseIndex >= pendingErrorsByCourse.length) return;

    const course = pendingErrorsByCourse[courseIndex];
    if (wordIndex < 0 || wordIndex >= course.words.length) return;

    // 切换字词选择状态
    course.words[wordIndex].selected = !course.words[wordIndex].selected;

    // 更新课程全选状态
    const selectedCount = course.words.filter(word => word.selected).length;
    course.allSelected = selectedCount === course.words.length;

    // 更新选中列表
    this.updateSelectedWords();

    this.setData({ pendingErrorsByCourse });
  },

  // 更新选中的字词列表
  updateSelectedWords() {
    const selectedWords = [];

    this.data.pendingErrorsByCourse.forEach(course => {
      course.words.forEach(word => {
        if (word.selected) {
          selectedWords.push(word);
        }
      });
    });

    // 更新全选状态
    const allSelected = this.data.totalPendingErrors > 0 &&
      selectedWords.length === this.data.totalPendingErrors;

    this.setData({
      selectedWords,
      allSelected
    });
  },

  // 全选/取消全选
  onToggleSelectAll() {
    const { pendingErrorsByCourse, allSelected } = this.data;
    const newSelectState = !allSelected;

    pendingErrorsByCourse.forEach(course => {
      course.allSelected = newSelectState;
      course.words.forEach(word => {
        word.selected = newSelectState;
      });
    });

    this.updateSelectedWords();
    this.setData({ pendingErrorsByCourse });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡，防止按钮点击时触发字词选择
  },

  // 开始错题挑战
  onStartErrorChallenge() {
    const { selectedWords } = this.data;

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请选择要练习的错题',
        icon: 'none'
      });
      return;
    }

    // 获取主要课程信息（选择最多错题的课程作为主课程）
    const courseGroups = {};
    selectedWords.forEach(error => {
      const courseId = error.courseInfo?.courseId || 'unknown';
      if (!courseGroups[courseId]) {
        courseGroups[courseId] = {
          courseInfo: error.courseInfo,
          count: 0
        };
      }
      courseGroups[courseId].count++;
    });

    // 找到错题最多的课程作为主课程
    const mainCourse = Object.values(courseGroups).reduce((max, current) =>
      current.count > max.count ? current : max
    );

    console.log('错题挑战 - 主课程信息:', mainCourse.courseInfo);
    console.log('错题挑战 - 选择的错题课程分布:', courseGroups);

    // 构建错题挑战数据，保留原有错题的课程信息
    const challengeWords = selectedWords.map(error => ({
      id: error.id,
      word: error.word,
      pinyin: error.pinyin,
      table: error.table || 'errorReview',
      originalCourseInfo: error.courseInfo // 保留原始课程信息用于订正时查找
    }));

    // 创建错题挑战会话 - 使用原有课程信息而不是创建新课程
    const challenge = {
      // 保持原有课程的核心信息
      courseId: mainCourse.courseInfo?.courseId || 'error_review',
      courseName: mainCourse.courseInfo?.courseName || '错题复习',
      courseTitle: mainCourse.courseInfo?.courseTitle || `错题练习 (${selectedWords.length}个字词)`,
      publisher: mainCourse.courseInfo?.publisher || '错题集',
      grade: mainCourse.courseInfo?.grade || '复习',
      term: mainCourse.courseInfo?.term || 'error',
      publisherId: mainCourse.courseInfo?.publisherId || 'error_review',
      gradeId: mainCourse.courseInfo?.gradeId || 'review',

      // 错题复习特有的信息
      selectedWords: challengeWords,
      timestamp: new Date().toISOString(),
      isErrorReview: true, // 标记为错题复习

      // 保存所有涉及的课程信息，用于多课程错题的处理
      involvedCourses: Object.keys(courseGroups).map(courseId => ({
        courseId,
        courseInfo: courseGroups[courseId].courseInfo,
        errorCount: courseGroups[courseId].count
      }))
    };

    console.log('错题挑战会话已创建:', challenge);

    // 保存挑战会话到全局数据
    app.globalData.currentChallenge = challenge;

    wx.showLoading({
      title: '准备错题挑战...'
    });

    // 跳转到练习页面
    setTimeout(() => {
      wx.hideLoading();

      wx.redirectTo({
        url: '/pages/practice/practice',
        success: () => {
          console.log('错题挑战开始，共选择', selectedWords.length, '个错题');
          console.log('涉及课程数量:', Object.keys(courseGroups).length);
        }
      });
    }, 1000);
  },

  // 标记错题为已订正
  onMarkAsCorreected(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex >= 0 && courseIndex < pendingErrorsByCourse.length &&
      wordIndex >= 0 && wordIndex < pendingErrorsByCourse[courseIndex].words.length) {

      const word = pendingErrorsByCourse[courseIndex].words[wordIndex];

      wx.showModal({
        title: '确认订正',
        content: `确定将"${word.word}"标记为已订正吗？`,
        success: (res) => {
          if (res.confirm) {
            this.markWordAsCorrected(word.id);
          }
        }
      });
    }
  },

  // 标记字词为已订正
  markWordAsCorrected(wordId) {
    if (SimpleStorage.markErrorWordCorrected(wordId)) {

      wx.showToast({
        title: '已标记为订正',
        icon: 'success'
      });

      // 重新加载数据
      this.loadErrorWords();

      // **新增：更新错题集徽章**
      try {
        const app = getApp();
        if (app && app.updateTabBarBadgeDelayed) {
          app.updateTabBarBadgeDelayed();
          console.log('📍 错题订正后更新TabBar徽章');
        }
      } catch (badgeError) {
        console.error('更新TabBar徽章失败:', badgeError);
      }
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true });

    setTimeout(() => {
      this.loadErrorWords();
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 页面分享
  onShareAppMessage() {
    const { totalPendingErrors } = this.data;
    return {
      title: `我的错题本有${totalPendingErrors}个待处理错题，一起来练习吧！`,
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-wrongbook.png'
    };
  },

  // 去练习
  onGoToPractice() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 调试错题数据
  debugErrorWords() {
    const errorWords = SimpleStorage.getErrorWords();
    console.log('调试：当前错题数据', errorWords);

    // 检查是否有问题数据
    const problemWords = errorWords.filter(error =>
      error.word === '暂无答案' || error.word === '未知字词' ||
      error.pinyin === '暂无拼音' || !error.word ||
      error.word === '无字词' || error.pinyin === '无拼音'
    );

    if (problemWords.length > 0) {
      console.log('发现问题数据:', problemWords);
      wx.showModal({
        title: '数据问题',
        content: `发现${problemWords.length}条错误数据，是否清除这些无效数据？`,
        success: (res) => {
          if (res.confirm) {
            this.cleanErrorData();
          }
        }
      });
    }
  },

  // 清理错误数据
  cleanErrorData() {
    const errorWords = SimpleStorage.getErrorWords();
    const validWords = errorWords.filter(error =>
      error.word &&
      error.word !== '暂无答案' &&
      error.word !== '未知字词' &&
      error.word !== '无字词' &&
      error.pinyin !== '暂无拼音' &&
      error.pinyin !== '无拼音'
    );

    SimpleStorage.setErrorWords(validWords);
    wx.showToast({
      title: `已清除${errorWords.length - validWords.length}条无效数据`,
      icon: 'success',
      duration: 2000
    });

    // 重新加载数据
    this.loadErrorWords();
  },

  // 手动刷新数据
  onManualRefresh() {
    console.log('用户手动刷新错题集数据');
    wx.showLoading({
      title: '刷新中...',
      mask: true
    });

    // 延迟一下再刷新，确保loading显示
    setTimeout(() => {
      this.loadErrorWords();
      wx.hideLoading();

      wx.showToast({
        title: '数据已刷新',
        icon: 'success',
        duration: 1000
      });
    }, 500);
  },

  // 长按头部触发调试功能
  onLongPressHeader() {
    wx.showActionSheet({
      itemList: ['查看缓存数据', '查看错题详情', '显示汉字缓存统计', '清除汉字缓存', '清空错题', '调试错题订正状态', '验证订正修复效果'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.showStorageData();
            break;
          case 1:
            this.showErrorDetails();
            break;
          case 2:
            this.showCacheStats();
            break;
          case 3:
            this.clearHanziCache();
            break;
          case 4:
            this.clearAllErrors();
            break;
          case 5:
            this.debugErrorCorrectionStatus();
            break;
          case 6:
            // 验证订正修复效果
            this.performCorrectnessVerification();
            break;
        }
      }
    });
  },

  // 调试错题订正状态
  debugErrorCorrectionStatus() {
    console.log('=== 错题订正状态调试开始 ===');
    return
    const errorWords = SimpleStorage.getErrorWords();
    const practiceRecords = SimpleStorage.getPracticeHistory();
    const learningProgress = SimpleStorage.getLearningProgress();

    console.log('错题总数:', errorWords.length);
    console.log('练习记录总数:', practiceRecords.length);
    console.log('学习进度记录数:', Object.keys(learningProgress).length);

    // 分析错题状态
    const correctedErrors = errorWords.filter(e => e.corrected === true);
    const uncorrectedErrors = errorWords.filter(e => !e.corrected);

    console.log('\n=== 错题状态分析 ===');
    console.log('已订正错题:', correctedErrors.length);
    console.log('未订正错题:', uncorrectedErrors.length);

    // 按课程分组分析
    const errorsByCourse = {};
    errorWords.forEach(error => {
      const courseId = error.courseInfo?.courseId || 'unknown';
      if (!errorsByCourse[courseId]) {
        errorsByCourse[courseId] = {
          courseName: error.courseInfo?.courseName || '未知课程',
          total: 0,
          corrected: 0,
          uncorrected: 0
        };
      }
      errorsByCourse[courseId].total++;
      if (error.corrected) {
        errorsByCourse[courseId].corrected++;
      } else {
        errorsByCourse[courseId].uncorrected++;
      }
    });

    console.log('\n=== 按课程错题分析 ===');
    Object.keys(errorsByCourse).forEach(courseId => {
      const stats = errorsByCourse[courseId];
      console.log(`课程 ${courseId} (${stats.courseName}):`);
      console.log(`  总错题: ${stats.total}`);
      console.log(`  已订正: ${stats.corrected}`);
      console.log(`  未订正: ${stats.uncorrected}`);
    });

    // 检查最近的练习记录中是否有跳过情况
    console.log('\n=== 最近练习记录分析 ===');
    const recentRecords = practiceRecords.slice(-5); // 最近5条记录
    recentRecords.forEach((record, index) => {
      console.log(`练习记录 ${index + 1}:`);
      console.log(`  课程: ${record.courseInfo?.courseName || '未知'}`);
      console.log(`  时间: ${record.timestamp || '未知'}`);
      console.log(`  总题数: ${record.results?.length || 0}`);

      if (record.results) {
        const correctCount = record.results.filter(r => r === true).length;
        const wrongCount = record.results.filter(r => r === false).length;
        const skippedCount = record.results.filter(r => r === null).length;

        console.log(`  正确: ${correctCount}, 错误: ${wrongCount}, 跳过: ${skippedCount}`);

        if (skippedCount > 0) {
          console.log(`  ⚠️ 发现跳过字词，需要验证是否正确处理订正`);

          // 显示跳过的具体字词
          record.words?.forEach((word, wordIndex) => {
            if (record.results[wordIndex] === null) {
              console.log(`    跳过的字词: ${word.word}`);
            }
          });
        }
      }
    });

    // 检查最近是否有错误订正的情况
    const recentCorrected = correctedErrors.filter(error => {
      if (!error.correctedTime) return false;
      const correctedTime = new Date(error.correctedTime);
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      return correctedTime > oneDayAgo;
    });

    console.log('\n=== 最近24小时订正情况 ===');
    console.log('最近24小时订正数量:', recentCorrected.length);
    recentCorrected.forEach(error => {
      console.log(`订正: ${error.word} (${error.correctionContext || '未知方式'})`);
    });

    // 生成调试报告
    let debugReport = `错题订正状态调试报告\n\n`;
    debugReport += `错题总数: ${errorWords.length}\n`;
    debugReport += `已订正: ${correctedErrors.length}\n`;
    debugReport += `未订正: ${uncorrectedErrors.length}\n`;
    debugReport += `最近24小时订正: ${recentCorrected.length}\n\n`;

    debugReport += `按课程分布:\n`;
    Object.keys(errorsByCourse).forEach(courseId => {
      const stats = errorsByCourse[courseId];
      debugReport += `${stats.courseName}: 总${stats.total}, 已订正${stats.corrected}, 未订正${stats.uncorrected}\n`;
    });

    const hasRecentSkips = recentRecords.some(record => record.results && record.results.includes(null));
    if (hasRecentSkips) {
      debugReport += `\n⚠️ 发现最近练习中有跳过字词\n请验证跳过字词是否正确地没有订正错题`;
    }

    // 显示调试报告
    wx.showModal({
      title: '错题订正状态调试',
      content: debugReport,
      showCancel: true,
      cancelText: '导出日志',
      confirmText: '确定',
      success: (res) => {
        if (res.cancel) {
          // 导出详细日志到控制台
          console.log('\n=== 详细错题列表 ===');
          errorWords.forEach((error, index) => {
            console.log(`错题 ${index + 1}:`);
            console.log(`  字词: ${error.word}`);
            console.log(`  课程: ${error.courseInfo?.courseName || '未知'} (${error.courseInfo?.courseId})`);
            console.log(`  订正状态: ${error.corrected ? '已订正' : '未订正'}`);
            console.log(`  订正时间: ${error.correctedTime || '无'}`);
            console.log(`  订正方式: ${error.correctionMethod || '无'}`);
            console.log(`  订正上下文: ${error.correctionContext || '无'}`);
          });

          wx.showToast({
            title: '详细日志已输出到控制台',
            icon: 'success'
          });
        }
      }
    });

    console.log('=== 错题订正状态调试结束 ===');
  },

  // 调用其他页面的调试功能
  callExternalDebugFunctions() {
    wx.showActionSheet({
      itemList: ['清除所有测试数据', '数据对比分析'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 清除所有测试数据
            wx.showModal({
              title: '清除测试数据',
              content: '确定要清除所有练习记录、错题记录和学习进度吗？此操作不可恢复。',
              success: (res) => {
                if (res.confirm) {
                  wx.clearStorageSync();
                  wx.showToast({
                    title: '所有数据已清除',
                    icon: 'success',
                    duration: 2000
                  });

                  // 重新加载页面
                  setTimeout(() => {
                    wx.reLaunch({
                      url: '/pages/courses/courses'
                    });
                  }, 2000);
                }
              }
            });
            break;
          case 1:
            // 数据对比分析（模拟挑战确认页面的功能）
            this.performDataComparison();
            break;
        }
      }
    });
  },

  // 执行数据对比分析
  performDataComparison() {
    const practiceRecords = SimpleStorage.get('practiceRecords') || [];
    const errorWords = SimpleStorage.getErrorWords();
    const learningProgress = SimpleStorage.getLearningProgress();

    console.log('=== 数据对比分析开始 ===');
    console.log('练习记录总数:', practiceRecords.length);
    console.log('错题记录总数:', errorWords.length);
    console.log('学习进度记录数:', Object.keys(learningProgress).length);

    // 分析每个练习记录与错题记录的对应关系
    practiceRecords.forEach((record, index) => {
      if (!record.results || !record.words) return;

      console.log(`\n练习记录 ${index + 1} 分析:`);
      console.log(`课程: ${record.courseInfo?.courseName || '未知'}`);
      console.log(`时间: ${record.timestamp || '未知'}`);

      const wrongAnswers = [];
      record.results.forEach((result, wordIndex) => {
        if (result === false) {
          const word = record.words[wordIndex];
          if (word) {
            wrongAnswers.push(word.word);
          }
        }
      });

      if (wrongAnswers.length > 0) {
        console.log(`该练习的错题: ${wrongAnswers.join(', ')}`);

        // 检查这些错题是否在错题记录中
        wrongAnswers.forEach(wrongWord => {
          const matchingErrors = errorWords.filter(error =>
            error.word === wrongWord &&
            error.courseInfo?.courseId === record.courseInfo?.courseId
          );

          if (matchingErrors.length === 0) {
            console.log(`⚠️ 错题记录中未找到: ${wrongWord}`);
          } else {
            console.log(`✓ 错题记录中找到 ${matchingErrors.length} 条: ${wrongWord}`);
          }
        });
      }
    });

    wx.showToast({
      title: '数据对比分析完成，请查看控制台',
      icon: 'success',
      duration: 2000
    });

    console.log('=== 数据对比分析结束 ===');
  },

  // 显示存储数据
  showStorageData() {
    const errorWords = SimpleStorage.getErrorWords();
    const cachedResults = SimpleStorage.getLatestPracticeResults();

    let content = `错题数据:\n总数: ${errorWords.length}\n`;
    content += `已订正: ${errorWords.filter(e => e.corrected).length}\n`;
    content += `未订正: ${errorWords.filter(e => !e.corrected).length}\n\n`;

    if (cachedResults) {
      content += `缓存结果:\n${JSON.stringify(cachedResults, null, 2)}`;
    } else {
      content += '无缓存结果';
    }

    // 添加汉字数据缓存统计
    if (wx.hanziWriterCache) {
      const cacheStats = wx.hanziWriterCache.getStats();
      content += `\n\n汉字数据缓存统计:\n`;
      content += `命中次数: ${cacheStats.hitCount}\n`;
      content += `未命中次数: ${cacheStats.missCount}\n`;
      content += `缓存命中率: ${cacheStats.hitRate}\n`;
      content += `缓存大小: ${cacheStats.totalSize}\n`;
      content += `已缓存字符: ${cacheStats.cacheKeys.join(', ')}`;
    }

    console.log('存储数据详情:', { errorWords, cachedResults });

    wx.showModal({
      title: '存储数据',
      content: content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 清除所有错题数据
  clearAllErrors() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有错题数据吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          SimpleStorage.clearErrorWords();
          wx.showToast({
            title: '数据已清除',
            icon: 'success'
          });
          // 重新加载数据
          this.loadErrorWords();
        }
      }
    });
  },

  // 长按字词项显示操作菜单
  onLongPressWord(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex >= 0 && courseIndex < pendingErrorsByCourse.length &&
      wordIndex >= 0 && wordIndex < pendingErrorsByCourse[courseIndex].words.length) {

      const word = pendingErrorsByCourse[courseIndex].words[wordIndex];

      wx.showActionSheet({
        itemList: ['标记为已订正', '删除错题'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 标记为已订正
            this.markWordAsCorrected(word.id);
          } else if (res.tapIndex === 1) {
            // 删除错题
            this.deleteErrorWord(word.id);
          }
        }
      });
    }
  },

  // 删除错题
  deleteErrorWord(wordId) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个错题吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          const errorWords = SimpleStorage.getErrorWords();
          const filteredWords = errorWords.filter(error => error.id !== wordId);

          SimpleStorage.setErrorWords(filteredWords);

          wx.showToast({
            title: '已删除',
            icon: 'success'
          });

          // 重新加载数据
          this.loadErrorWords();
        }
      }
    });
  },

  // 检查是否有错题复习结果
  checkErrorReviewResult() {
    const cachedResults = SimpleStorage.getLatestPracticeResults();
    if (cachedResults && cachedResults.isErrorReview) {
      console.log('检测到错题复习完成，缓存结果:', cachedResults);

      // 清除缓存的练习结果
      SimpleStorage.clearLatestPracticeResults();

      let message = '错题状态已更新';
      if (cachedResults.detailedLog) {
        message = cachedResults.detailedLog;
      } else if (cachedResults.correctedCount > 0) {
        message = `已订正 ${cachedResults.correctedCount} 个错题`;
      }

      wx.showToast({
        title: message,
        icon: 'success',
        duration: 2000
      });
    } else {
      console.log('没有检测到错题复习缓存结果');
    }
  },

  // 智能分割拼音字符串
  splitPinyinString(pinyinString, wordLength) {
    // 如果已经有空格分隔，直接分割
    if (pinyinString.includes(' ')) {
      const parts = pinyinString.trim().split(/\s+/);
      if (parts.length === wordLength) {
        return parts;
      }
    }

    // 对于连续的拼音字符串，尝试智能分割
    if (wordLength === 1) {
      return [pinyinString];
    }

    // 简单的拼音分割规则：按声母韵母分割
    // 这是一个简化版本，实际可能需要更复杂的拼音库
    const pinyinRegex = /[bpmfdtnlgkhjqxzcsrwy]*[aeiouv]+[ng]?[1-4]?/gi;
    const matches = pinyinString.match(pinyinRegex) || [];

    if (matches.length === wordLength) {
      return matches;
    }

    // 如果无法智能分割，尝试平均分割
    if (wordLength > 1) {
      const avgLength = Math.floor(pinyinString.length / wordLength);
      const parts = [];
      for (let i = 0; i < wordLength; i++) {
        const start = i * avgLength;
        const end = i === wordLength - 1 ? pinyinString.length : (i + 1) * avgLength;
        parts.push(pinyinString.substring(start, end));
      }
      return parts;
    }

    // 默认情况：第一个字符显示完整拼音，其他为空
    const result = Array(wordLength).fill('');
    result[0] = pinyinString;
    return result;
  },

  // 预初始化汉字书写器组件（页面加载时调用）
  preInitializeHanziWriter() {
    //console.log('预初始化汉字书写器组件');

    try {
      // 检查组件是否存在
      const writerComponent = this.selectComponent('#hz-writer');
      if (!writerComponent) {
        console.warn('汉字书写器组件未找到，稍后重试');
        setTimeout(() => {
          this.preInitializeHanziWriter();
        }, 1000);
        return;
      }

      // 等待组件内部Canvas完全初始化
      setTimeout(() => {
        console.log('汉字书写器组件已准备就绪');
        this._writerComponentReady = true;
      }, 1000);
    } catch (error) {
      console.error('预初始化汉字书写器组件失败:', error);
    }
  },

  // 显示汉字缓存统计
  showCacheStats() {
    if (!wx.hanziWriterCache) {
      wx.showToast({
        title: '缓存功能不可用',
        icon: 'none'
      });
      return;
    }

    const stats = wx.hanziWriterCache.getStats();
    let content = `汉字数据缓存统计:\n\n`;
    content += `缓存命中次数: ${stats.hitCount}\n`;
    content += `缓存未命中次数: ${stats.missCount}\n`;
    content += `缓存命中率: ${stats.hitRate}\n`;
    content += `当前缓存大小: ${stats.totalSize}\n\n`;
    content += `已缓存的字符:\n${stats.cacheKeys.join(', ')}`;

    wx.showModal({
      title: '汉字缓存统计',
      content: content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 清除汉字缓存
  clearHanziCache() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除汉字数据缓存吗？这将导致下次查看笔画时需要重新加载。',
      success: (res) => {
        if (res.confirm && wx.hanziWriterCache) {
          wx.hanziWriterCache.clearCache();
          wx.showToast({
            title: '缓存已清除',
            icon: 'success'
          });
        }
      }
    });
  },

  // ============ 随机挑战功能 ============

  /**
   * 增加挑战字数
   */
  onIncreaseWordCount() {
    const currentCount = this.data.randomChallengeWordCount;
    const maxCount = Math.min(36, this.data.totalHistoryErrors); // 限制最大值为36

    if (currentCount < maxCount) {
      this.setData({
        randomChallengeWordCount: Math.min(currentCount + 1, maxCount)
      });
    }
  },

  /**
   * 减少挑战字数
   */
  onDecreaseWordCount() {
    const currentCount = this.data.randomChallengeWordCount;

    if (currentCount > 1) {
      this.setData({
        randomChallengeWordCount: Math.max(currentCount - 1, 1)
      });
    }
  },

  /**
   * 开始随机挑战
   */
  onRandomChallenge() {
    // 确保挑战字数不超过36
    const challengeCount = Math.min(36, this.data.randomChallengeWordCount);
    const totalHistoryErrors = this.data.totalHistoryErrors;

    if (totalHistoryErrors === 0) {
      wx.showToast({
        title: '没有历史错题',
        icon: 'none'
      });
      return;
    }

    if (challengeCount <= 0) {
      wx.showToast({
        title: '请选择挑战字数',
        icon: 'none'
      });
      return;
    }

    // 收集所有历史错题
    const allHistoryWords = [];
    this.data.historyErrorsByCourse.forEach(course => {
      course.words.forEach(word => {
        allHistoryWords.push({
          ...word,
          courseInfo: {
            courseName: course.courseName,
            courseId: course.courseKey,
            ...word.courseInfo
          }
        });
      });
    });

    if (allHistoryWords.length === 0) {
      wx.showToast({
        title: '没有可用的历史错题',
        icon: 'none'
      });
      return;
    }

    // 随机选择指定数量的字词
    const selectedWords = this.getRandomWords(allHistoryWords, challengeCount);

    console.log('随机挑战选中的字词:', selectedWords);

    // 构造挑战数据
    const challengeData = {
      mode: 'random-challenge', // 标识为随机挑战模式
      words: selectedWords.map(word => ({
        word: word.word,
        pinyin: word.pinyin,
        courseInfo: word.courseInfo
      })),
      title: `随机挑战 ${challengeCount} 个错题`,
      description: '从历史错题中随机选择，加强练习'
    };

    // 将挑战数据存储到临时存储
    SimpleStorage.set(SimpleStorage.KEYS.RANDOM_CHALLENGE_DATA, challengeData);

    // 跳转到练习页面
    wx.navigateTo({
      url: `/pages/practice/practice?mode=random-challenge&count=${challengeCount}`,
      success: () => {
        console.log('成功跳转到随机挑战练习页面');
      },
      fail: (err) => {
        console.error('跳转到练习页面失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 从数组中随机选择指定数量的元素（不重复）
   */
  getRandomWords(words, count) {
    if (words.length <= count) {
      return [...words]; // 如果总数不够，返回所有的
    }

    const result = [];
    const usedIndices = new Set();

    while (result.length < count) {
      const randomIndex = Math.floor(Math.random() * words.length);

      if (!usedIndices.has(randomIndex)) {
        usedIndices.add(randomIndex);
        result.push(words[randomIndex]);
      }
    }

    return result;
  },

  // 点击历史记录字词显示笔画动画
  onShowStrokeAnimation(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { historyErrorsByCourse } = this.data;

    if (courseIndex >= 0 && courseIndex < historyErrorsByCourse.length &&
      wordIndex >= 0 && wordIndex < historyErrorsByCourse[courseIndex].words.length) {

      const word = historyErrorsByCourse[courseIndex].words[wordIndex];
      console.log('点击字词显示笔画动画:', word.word, word.pinyin);

      // 跳转到stroke-demo页面，同时传递拼音参数
      const encodedWord = encodeURIComponent(word.word);
      const encodedPinyin = encodeURIComponent(word.pinyin || '');
      wx.navigateTo({
        url: `/pages/stroke-demo/stroke-demo?word=${encodedWord}&pinyin=${encodedPinyin}&from=wrongbook`,
        success: () => {
          console.log('成功跳转到笔画演示页面');
        },
        fail: (error) => {
          console.error('跳转到笔画演示页面失败:', error);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },

  // 显示错题详情
  showErrorDetails() {
    const errorWords = SimpleStorage.getErrorWords();

    let details = `错题总数: ${errorWords.length}\n\n`;

    errorWords.forEach((error, index) => {
      details += `${index + 1}. ${error.word}\n`;
      details += `   课程: ${error.courseInfo?.courseName || '未知'}\n`;
      details += `   状态: ${error.corrected ? '已订正' : '未订正'}\n`;
      if (error.correctedTime) {
        details += `   订正时间: ${new Date(error.correctedTime).toLocaleString()}\n`;
      }
      details += '\n';
    });

    wx.showModal({
      title: '错题详情',
      content: details,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 验证订正修复效果
  performCorrectnessVerification() {
    console.log('=== 验证错题订正修复效果 ===');

    const errorWords = SimpleStorage.getErrorWords();
    const practiceRecords = SimpleStorage.getPracticeHistory();

    if (errorWords.length === 0) {
      wx.showModal({
        title: '验证结果',
        content: '当前没有错题记录，无需验证。\n请先进行一些练习产生错题。',
        showCancel: false
      });
      return;
    }

    // 查找近期的练习记录
    const recentRecords = practiceRecords.slice(-3); // 最近3条记录
    let verificationReport = `错题订正修复效果验证\n\n`;
    verificationReport += `当前错题总数: ${errorWords.length}\n`;
    verificationReport += `已订正: ${errorWords.filter(e => e.corrected).length}\n`;
    verificationReport += `未订正: ${errorWords.filter(e => !e.corrected).length}\n\n`;

    // 检查最近练习是否有答对错题字词的情况
    let hasCorrectionsExpected = false;
    let actualCorrections = 0;

    recentRecords.forEach((record, index) => {
      if (!record.words || !record.results) return;

      verificationReport += `最近练习 ${index + 1}:\n`;
      verificationReport += `课程: ${record.courseInfo?.courseName || '未知'}\n`;

      // 检查该练习中是否有答对的字词在错题记录中存在
      const correctAnswers = [];
      record.results.forEach((result, wordIndex) => {
        if (result === true) { // 答对的字词
          const word = record.words[wordIndex];
          if (word) {
            correctAnswers.push(word.word);

            // 检查这个字词是否在错题记录中
            const matchingErrors = errorWords.filter(error =>
              error.word === word.word && !error.corrected
            );

            if (matchingErrors.length > 0) {
              hasCorrectionsExpected = true;
              verificationReport += `  ⚠️ "${word.word}" 答对了但错题记录仍未订正\n`;
            }
          }
        }
      });

      if (correctAnswers.length > 0) {
        verificationReport += `  答对字词: ${correctAnswers.join(', ')}\n`;
      }
      verificationReport += '\n';
    });

    // 统计实际的订正数据
    const recentCorrections = errorWords.filter(error => {
      if (!error.correctedTime) return false;
      const correctedTime = new Date(error.correctedTime);
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      return correctedTime > oneDayAgo;
    });

    actualCorrections = recentCorrections.length;
    verificationReport += `最近24小时订正数量: ${actualCorrections}\n`;

    if (actualCorrections > 0) {
      verificationReport += `订正详情:\n`;
      recentCorrections.forEach(error => {
        verificationReport += `  "${error.word}" (${error.correctionContext || '未知方式'})\n`;
      });
    }

    verificationReport += '\n';

    // 判断修复效果
    if (hasCorrectionsExpected && actualCorrections === 0) {
      verificationReport += `❌ 可能存在问题：发现应该被订正的字词但没有订正记录\n`;
      verificationReport += `建议：\n`;
      verificationReport += `1. 检查控制台日志确认订正逻辑是否正常执行\n`;
      verificationReport += `2. 进行一次新的练习测试订正功能\n`;
    } else if (actualCorrections > 0) {
      verificationReport += `✅ 修复效果良好：发现正常的订正记录\n`;
    } else {
      verificationReport += `ℹ️ 暂无足够数据进行验证\n`;
      verificationReport += `建议：进行一次包含错题字词的练习来测试订正功能\n`;
    }

    wx.showModal({
      title: '验证订正修复效果',
      content: verificationReport,
      showCancel: true,
      cancelText: '详细日志',
      confirmText: '确定',
      success: (res) => {
        if (res.cancel) {
          console.log('\n=== 详细验证数据 ===');
          console.log('错题记录:', errorWords);
          console.log('最近练习记录:', recentRecords);
          console.log('最近订正记录:', recentCorrections);

          wx.showToast({
            title: '详细验证数据已输出到控制台',
            icon: 'success'
          });
        }
      }
    });

    console.log('=== 验证完成 ===');
  },




});
